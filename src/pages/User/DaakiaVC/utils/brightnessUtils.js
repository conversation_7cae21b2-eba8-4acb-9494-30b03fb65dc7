// Brightness manager utility - stateless functions like virtualBackground.js
import { DataReceivedEvent } from "./constants";

// Debug flag - set to true to see console logs, false to hide them
const DEBUG_BRIGHTNESS = false;

let participantBrightness = new Map();
let throttleTimer = null;
let lastThrottleTime = 0;

// Handle brightness data received from other participants
export function handleBrightnessDataReceived(data, participant) {
  try {
    if (data.action === DataReceivedEvent.BRIGHTNESS_CHANGE) {
      const { brightness } = data;
      if (DEBUG_BRIGHTNESS) {
        console.log(`Received brightness ${brightness} from participant ${participant.identity}`);
      }
      participantBrightness.set(participant.identity, brightness);
    }
  } catch (error) {
    console.error('Brightness data processing failed:', error.message);
  }
}

// Clean up participant brightness when they disconnect
export function handleParticipantDisconnected(participant) {
  participantBrightness.delete(participant.identity);
  if (DEBUG_BRIGHTNESS) {
    console.log(`Removed brightness data for disconnected participant ${participant.identity}`);
  }
}

// Clear all brightness data when room disconnects
export function handleRoomDisconnected() {
  participantBrightness.clear();
  if (DEBUG_BRIGHTNESS) {
    console.log('Cleared all brightness data on room disconnect');
  }
}

// Send brightness data immediately using data channels
function sendBrightnessImmediate(room, brightness) {
  try {
    const remoteParticipants = Array.from(room.remoteParticipants.values());

    if (remoteParticipants.length === 0) {
      if (DEBUG_BRIGHTNESS) console.log('sendBrightnessImmediate: No remote participants to send brightness to');
      return;
    }

    if (DEBUG_BRIGHTNESS) console.log(`sendBrightnessImmediate: Sending brightness ${brightness} to ${remoteParticipants.length} participants`);

    // Prepare data for data channel
    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.BRIGHTNESS_CHANGE,
        brightness: brightness,
      })
    );

    // Send to all participants via data channel
    room.localParticipant.publishData(data, { reliable: true });

    if (DEBUG_BRIGHTNESS) console.log(`sendBrightnessImmediate: Successfully sent brightness ${brightness} to all participants via data channel`);
  } catch (error) {
    console.error('sendBrightnessImmediate: Error sending brightness:', error.message);
  }
}

// Send brightness to all participants with throttling
export function sendBrightnessToAll(room, brightness) {
  if (DEBUG_BRIGHTNESS) console.log(`sendBrightnessToAll: Called with brightness ${brightness}, room state: ${room?.state}`);

  if (!room || room.state !== 'connected') {
    if (DEBUG_BRIGHTNESS) console.log('sendBrightnessToAll: Room not connected, skipping');
    return;
  }

  if (brightness === 100) {
    if (DEBUG_BRIGHTNESS) console.log('sendBrightnessToAll: Brightness is 100 (default), skipping send');
    return;
  }

  // Throttle the send operation for better UI responsiveness
  const now = Date.now();
  const throttleDelay = 150; // 150ms throttle for smooth UI updates

  if (now - lastThrottleTime < throttleDelay) {
    if (DEBUG_BRIGHTNESS) console.log(`sendBrightnessToAll: Throttling - scheduling delayed send in ${throttleDelay - (now - lastThrottleTime)}ms`);
    // Clear existing timer and set new one
    if (throttleTimer) {
      clearTimeout(throttleTimer);
    }

    throttleTimer = setTimeout(() => {
      lastThrottleTime = Date.now();
      if (DEBUG_BRIGHTNESS) console.log('sendBrightnessToAll: Executing throttled send');
      sendBrightnessImmediate(room, brightness);
    }, throttleDelay - (now - lastThrottleTime));
    return;
  }

  if (DEBUG_BRIGHTNESS) console.log('sendBrightnessToAll: Sending immediately (no throttle needed)');
  lastThrottleTime = now;
  sendBrightnessImmediate(room, brightness);
}

// Send brightness to a specific new participant
export function sendBrightnessToNewParticipant(room, participant, brightness) {
  if (!room || room.state !== 'connected') {
    return;
  }

  if (!participant || !participant.identity) {
    return;
  }

  if (brightness === 100) {
    return; // No need to send default brightness
  }

  // Wait a bit for participant to be ready, then send via data channel
  setTimeout(() => {
    try {
      const encoder = new TextEncoder();
      const data = encoder.encode(
        JSON.stringify({
          action: DataReceivedEvent.BRIGHTNESS_CHANGE,
          brightness: brightness,
        })
      );

      // Send to specific participant via data channel
      room.localParticipant.publishData(data, {
        reliable: true,
        destinationIdentities: [participant.identity],
      });

      if (DEBUG_BRIGHTNESS) {
        console.log(`sendBrightnessToNewParticipant: Sent brightness ${brightness} to new participant ${participant.identity}`);
      }
    } catch (error) {
      console.error(`Failed to send brightness to new participant ${participant.identity}:`, error.message);
    }
  }, 3000);
}

// Get all participant brightness values (simple getter)
export function getAllParticipantBrightness() {
  return new Map(participantBrightness);
}

// Clean up brightness utilities (much simpler now with data channels)
export function cleanupBrightness() {
  if (throttleTimer) {
    clearTimeout(throttleTimer);
    throttleTimer = null;
  }
  lastThrottleTime = 0;
  participantBrightness.clear();

  if (DEBUG_BRIGHTNESS) {
    console.log('Brightness utilities cleaned up');
  }
}
