import { ReactComponent as NoEffectIcon } from "../assets/icons/NoEffect.svg";
import { ReactComponent as Blur1Icon } from "../assets/icons/Blur_1.svg";
import { ReactComponent as Blur3Icon } from "../assets/icons/Blur_3.svg";

export const constants = {
  REGION_LOCATOR: process.env.REACT_APP_CLIENT_REGION_LOCATOR,
  STAG_BASE_URL: process.env.REACT_APP_STAG_BACKEND_BASE_URL,
  TEMP_BASE_URL: "https://c4de-110-235-229-19.ngrok-free.app/v2.0",
  NAME_KEY: process.env.REACT_APP_NAME_KEY || "Base App",
  CO_HOST_TOKEN: "CoHostToken",
  MEETING_USER_CHOICES: "MeetingUserChoices",
  MEETING_DETAILS: "meetingDetails",
  DATA_DOG_TOKEN: process.env.REACT_APP_DATA_DOG_TOKEN,
  DATA_DOG_ENV: process.env.REACT_APP_LOG_ENV,
  DATA_DOG_RUM_TOKEN: process.env.REACT_APP_DATA_DOG_RUM_TOKEN,
  DATA_DOG_RUM_APPLICATION_ID: process.env.REACT_APP_DATA_DOG_RUM_APPLICATION_ID,
  DATADOG_LOG_SERVICE: process.env.REACT_APP_DATADOG_LOG_SERVICE,
  DATADOG_RUM_SERVICE: process.env.REACT_APP_DATADOG_RUM_SERVICE,
  DATADOG_SITE: process.env.REACT_APP_DATADOG_SITE,
  SOCKET_URL: process.env.REACT_APP_SOCKET_SERVER_URL,
};

export const virtualBackground = [
  {
    heading: "Effects",
    effects: [
      {
        label: "No Effects",
        icon: <NoEffectIcon />,
        value: 0,
      },
      {
        label: "Light Blur",
        icon: <Blur1Icon />,
        value: 5,
      },
      {
        label: "Heavy Blur",
        icon: <Blur3Icon />,
        value: 20,
      },
    ],
  },
  {
    heading: "Office",
    effects: [
      {
        label: "Office 1",
        value:
          "https://cdn.vc.daakia.co.in/images/office_1.jpg",
        alt: "Office 1",
      },
      {
        label: "Office 2",
        value:
          "https://cdn.vc.daakia.co.in/images/office_2.jpg",
        alt: "Office 2",
      },
      {
        label: "Office 3",
        value:
          "https://cdn.vc.daakia.co.in/images/office_3.jpg",
        alt: "Office 3",
      },
    ],
  },
  {
    heading: "Room",
    effects: [
      {
        label: "Room 1",
        value: "https://cdn.vc.daakia.co.in/images/room_1.jpg",
        alt: "Room 1",
      },
      {
        label: "Room 2",
        value: "https://cdn.vc.daakia.co.in/images/room_2.jpg",
        alt: "Room 2",
      },
      {
        label: "Room 3",
        value: "https://cdn.vc.daakia.co.in/images/room_3.jpg",
        alt: "Room 3",
      },
      {
        label: "Room 4",
        value: "https://cdn.vc.daakia.co.in/images/room_4.jpg",
        alt: "Room 4",
      },
    ],
  },
  {
    heading: "Nature",
    effects: [
      {
        label: "Nature 1",
        value:
          "https://cdn.vc.daakia.co.in/images/nature_1.jpg",
        alt: "Nature 1",
      },
      {
        label: "Nature 2",
        value:
          "https://cdn.vc.daakia.co.in/images/nature_2.jpg",
        alt: "Nature 2",
      },
      {
        label: "Nature 3",
        value:
          "https://cdn.vc.daakia.co.in/images/nature_3.jpg",
        alt: "Nature 3",
      },
      {
        label: "Nature 4",
        value:
          "https://cdn.vc.daakia.co.in/images/nature_4.jpg",
        alt: "Nature 4",
      },
    ],
  },
];

export const reactions = [
  {
    id: 1,
    type: "hand",
    action: "lower",
  },
  {
    id: 2,
    type: "hand",
    action: "raise",
  },
  {
    id: 3,
    type: "reaction",
    action: "laugh",
  },
  {
    id: 4,
    type: "clap",
    action: "stop",
  },
  {
    id: 5,
    type: "thumbs-up",
    action: "start",
  },
  {
    id: 6,
    type: "thumbs-up",
    action: "stop",
  },
  {
    id: 7,
    type: "thumbs-down",
    action: "start",
  },
  {
    id: 8,
    type: "thumbs-down",
    action: "stop",
  },
];

export const DrawerState = Object.freeze({
  CHAT: "chat",
  PARTICIPANTS: "participants",
  HOSTCONTROL: "hostcontrol",
  BREAKOUTROOM: "breakoutRoom",
  VIRTUAL_BACKGROUND: "virtualBackground",
  REPORT_ISSUE: "reportIssue",
  LIVECAPTION: "livecaptions",
  RECORDING_CONSENT: "recordingConsent",
  NONE: null,
});

export const DataReceivedEvent = Object.freeze({
  RAISE_HAND: "raise_hand",
  STOP_RAISE_HAND: "stop_raise_hand",
  STOP_RAISE_HAND_ALL: "stop_raise_hand_all",
  ASK_TO_UNMUTE_MIC: "ask_to_unmute_mic",
  MUTE_MIC: "mute_mic",
  ASK_TO_UNMUTE_CAMERA: "ask_to_unmute_camera",
  MUTE_CAMERA: "mute_camera",
  SEND_PRIVATE_MESSAGE: "send_private_message",
  SEND_PUBLIC_MESSAGE: "send_public_message",
  HEART: "heart",
  BLUSH: "blush",
  CLAP: "clap",
  SMILE: "smile",
  THUMBS_UP: "thumbsUp",
  GRINNING_FACE: "grinningFace",
  MAKE_CO_HOST:"makeCoHost",
  REMOVE_CO_HOST:"removeCoHost",
  LOBBY: "lobby",
  FORCE_MUTE_ALL: "force_mute_all",
  FORCE_VIDEO_OFF_ALL: "force_video_off_all",
  BR_MOVE_PARTICIPANT: "breakout",
  BREAKOUT_ROOM_UPDATE: "breakout_room_update",
  LIVECAPTION:"live-caption",
  SHOW_LIVECAPTION:"show-live-caption",
  REQUEST_LIVECAPTION_DRAWER_STATE:"request-livecaption-drawer-state",
  CAN_DOWNLOAD_CHAT_ATTACHEMENT:"can-download-chat-attachment",
  WHITEBOARD_STATE:"whiteboard-state",
  WHITEBOARD_UPDATE:"whiteboard-update",
  WHITEBOARD_DATA_REQUEST:"whiteboard-data-request",
  ALLOW_LIVE_COLLAB_WHITEBOARD:"allow-live-collab-whiteboard",
  RECORDING_CONSENT_MODAL:"recording-consent-modal",
  RECORDING_CONSENT_STATUS:"recording-consent-status",
  ADD_REACTION: "add-reaction",
  BRIGHTNESS_CHANGE:"brightness-change",
  SCREEN_CAPTURE_TAKEN:"screen-capture-taken",
  EDIT_MESSAGE:"edit-message",
  DELETE_MESSAGE:"delete-message",
});

export const SocketChannel = Object.freeze({
  WHITEBOARD_DATA_REQUEST: "whiteboard-data-request",
  WHITEBOARD_UPDATE:"update-whiteboard"
});

export const darkColors = [
  '#56755c', '#4d3609', '#856c2c', '#713697', '#7a2739',
  '#da26a1', '#a85005', '#c9251c', '#70720f', '#476682',
  '#2d54d0', '#390977', '#5a565d', '#3a5775', '#a52185',
  '#1c6d85', '#042f48', '#bf1b2e', '#dc4528', '#0775d7',
  '#2e6ce3', '#711b0f', '#6617bf', '#b1324d', '#388c74',
  '#860e96', '#637505', '#0faea6', '#8d1c73', '#3c8b47',
  '#bd32ee', '#4971ae', '#eb074c', '#39174d', '#2759d5',
  '#138ec8', '#0628e2', '#070c8b', '#b10f09', '#6a15c8',
  '#298d7e', '#2b276c', '#ee0892', '#f025fc', '#405634',
  '#7849f3', '#143116', '#c82fbf', '#3b7813', '#3121b3'
];


export const PROCESS_EVENT = Object.freeze({
  MIC: 'mic',
  CAMERA:'CAMERA'
})


export const TOPIC_NAME = Object.freeze({
  AGENT_OPERATION: 'lk.operation',
  AGENT_TRANSCRIPTION: 'lk.transcription',
  CHAT: 'lk.chat'
})